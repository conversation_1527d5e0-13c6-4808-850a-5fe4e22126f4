"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useSidebarStore } from "@/stores/sidebarStore"
import {
    Brain,
    Code,
    Database,
    GitBranch,
    Globe,
    MessageSquare,
    Package,
    PanelLeftClose,
    RotateCcw,
    Route,
    Search,
    User,
    Workflow
} from "lucide-react"
import { useCallback } from "react"
import { getAllBlocks, getBlocksByCategory } from "../../blocks"
import { BlockItem } from "./BlockItem"
import { SidebarTabs } from "./SidebarTabs"

// Get blocks from registry
const toolBlocks = getBlocksByCategory('tools')
const blockBlocks = getBlocksByCategory('blocks')

const blockCategories = [
  {
    title: "Blocks",
    items: [
      { id: "agent", name: "Agent", description: "Build an agent", icon: User, color: "#802FFF" },
      { id: "api", name: "API", description: "Use any API", icon: Globe, color: "#2F55FF" },
      { id: "condition", name: "Condition", description: "Add a condition", icon: GitBranch, color: "#FF752F" },
      { id: "function", name: "Function", description: "Run custom logic", icon: Code, color: "#FF402F" },
      { id: "router", name: "Router", description: "Route workflow", icon: Route, color: "#28C43F" },
      { id: "memory", name: "Memory", description: "Add memory store", icon: Database, color: "#F64F9E" },
      { id: "knowledge", name: "Knowledge", description: "Use vector search", icon: Brain, color: "#1F40ED" },
      { id: "workflow", name: "Workflow", description: "Execute another workflow", icon: Workflow, color: "#FFC83C" },
      { id: "response", name: "Response", description: "Send structured API response", icon: MessageSquare, color: "#4D5FFF" },
      { id: "loop", name: "Loop", description: "Iterate over items", icon: RotateCcw, color: "#2FB3FF" }
    ]
  },
  {
    title: "Tools",
    items: toolBlocks.map(block => ({
      id: block.type,
      name: block.name,
      description: block.description,
      icon: block.icon,
      color: block.bgColor
    }))
  }
]

export function LeftSidebar() {
  const {
    isCollapsed,
    activeTab,
    searchQuery,
    toggleCollapsed,
    setActiveTab,
    setSearchQuery
  } = useSidebarStore()

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }, [setSearchQuery])

  const handleTabChange = useCallback((tab: "Blocks" | "Tools") => {
    setActiveTab(tab)
  }, [setActiveTab])

  // Filter blocks based on search query
  const filteredBlocks = blockCategories
    .find((cat) => cat.title === activeTab)
    ?.items.filter((block) => {
      if (!searchQuery.trim()) return true
      return (
        block.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        block.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }) || []

  return (
    <TooltipProvider>
      <div className={`bg-background flex flex-col transition-all duration-200 ease-in-out fixed inset-y-0 z-20 overflow-hidden ${isCollapsed ? 'w-0' : 'w-64 border-r border-border main-content-overlay'}`} style={{ left: '80px' }}>
        {!isCollapsed && (
          <div className="flex h-full flex-col">
          {/* Search */}
          <div className="sticky top-0 z-20 bg-background px-4 pt-4 pb-3">
            <div className="relative group">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground group-focus-within:text-primary-500 w-4 h-4 transition-colors" />
              <Input
                placeholder="Search blocks and tools"
                className="pl-9 pr-4 shadow-sm border-0 bg-neutral-50 hover:bg-neutral-100 focus-visible:bg-background focus-visible:shadow-md dark:bg-neutral-900 dark:hover:bg-neutral-800 transition-all duration-200"
                style={{ borderRadius: '2px' }}
                variant="filled"
                size="default"
                value={searchQuery}
                onChange={handleSearchChange}
                autoComplete="off"
                autoCorrect="off"
                autoCapitalize="off"
                spellCheck="false"
              />
            </div>
          </div>

          {/* Tabs */}
          {!searchQuery && (
            <div className="sticky top-[72px] z-20 bg-background">
              <SidebarTabs activeTab={activeTab} onTabChange={handleTabChange} />
            </div>
          )}

          {/* Block List */}
          <ScrollArea className="h-[calc(100%-4rem)]">
            <div className="pl-2 pr-6 pt-4 pb-20">
              <div className="flex flex-col gap-3">
                {filteredBlocks.map((block) => (
                  <BlockItem
                    key={block.id}
                    id={block.id}
                    name={block.name}
                    description={block.description}
                    icon={block.icon}
                    color={block.color}
                  />
                ))}
              </div>
            </div>
          </ScrollArea>

          {/* Bottom section */}
          <div className="absolute right-0 bottom-0 left-0 h-16 border-t bg-background">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleCollapsed}
                  className="absolute right-4 bottom-[18px] flex h-11 w-11 items-center justify-center rounded-sm text-gray-500 hover:bg-accent/50 hover:text-foreground p-2"
                >
                  <PanelLeftClose className="h-7 w-7" />
                  <span className="sr-only">Close Toolbar</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent
                side="left"
                className="bg-black text-white dark:bg-black dark:text-white border-none shadow-lg"
              >
                Close Toolbar
              </TooltipContent>
            </Tooltip>
          </div>
        </div>
        )}
      </div>
    </TooltipProvider>
  )
}
